import * as THREE from 'three';
import { CarPhysics } from '../physics/CarPhysics.js';

/**
 * Car entity with physics, rendering, and game logic
 * Highly configurable racing car with Wipeout-style handling
 */
export class Car {
    constructor(config) {
        this.config = config;
        this.physics = null;
        
        // Visual components
        this.mesh = null;
        this.wheels = [];
        this.exhaustTrail = null;
        this.speedLines = null;
        
        // Car state
        this.state = {
            speed: 0,
            boost: 100,
            isGrounded: false,
            position: new THREE.Vector3(),
            rotation: new THREE.Quaternion(),
            velocity: new THREE.Vector3()
        };
        
        // Visual effects
        this.effects = {
            exhaustParticles: null,
            speedLinesVisible: false,
            glowEffect: null
        };
        
        // Audio data for audio system
        this.audioData = {
            speed: 0,
            rpm: 1000,
            throttle: 0,
            boost: false
        };
    }

    /**
     * Initialize car
     */
    async init(scene, physicsWorld) {
        // Create physics
        this.physics = new CarPhysics(this.config, physicsWorld);
        const physicsBody = this.physics.init();
        
        // Create visual representation
        await this.createMesh();
        
        // Add to scene
        scene.add(this.mesh);
        
        // Create visual effects
        this.createVisualEffects(scene);
        
        // Register physics body with mesh
        physicsWorld.addBody(physicsBody, this.mesh, 'car');
        
        console.log('🏎️ Car initialized');
    }

    /**
     * Create car mesh
     */
    async createMesh() {
        // Create car body (returns a group with multiple meshes)
        this.mesh = this.createCarBodyGeometry();
        this.mesh.castShadow = true;
        this.mesh.receiveShadow = true;
        
        // Create wheels
        this.createWheels();
        
        // Create additional details
        this.createCarDetails();
    }

    /**
     * Create car body geometry
     */
    createCarBodyGeometry() {
        const dims = this.config.physics.dimensions;
        
        // Create a more interesting car shape using multiple geometries
        const group = new THREE.Group();
        
        // Main body
        const mainBodyGeometry = new THREE.BoxGeometry(dims.width, dims.height * 0.6, dims.length);
        const mainBodyMaterial = new THREE.MeshPhongMaterial({ 
            color: 0x0066ff,
            shininess: 100,
            specular: 0x222222
        });
        const mainBody = new THREE.Mesh(mainBodyGeometry, mainBodyMaterial);
        mainBody.position.y = dims.height * 0.2;
        group.add(mainBody);
        
        // Cockpit
        const cockpitGeometry = new THREE.BoxGeometry(dims.width * 0.8, dims.height * 0.4, dims.length * 0.4);
        const cockpitMaterial = new THREE.MeshPhongMaterial({ 
            color: 0x001133,
            transparent: true,
            opacity: 0.7
        });
        const cockpit = new THREE.Mesh(cockpitGeometry, cockpitMaterial);
        cockpit.position.y = dims.height * 0.5;
        cockpit.position.z = dims.length * 0.1;
        group.add(cockpit);
        
        // Front nose
        const noseGeometry = new THREE.ConeGeometry(dims.width * 0.3, dims.length * 0.3, 8);
        const noseMaterial = new THREE.MeshPhongMaterial({ 
            color: 0x004488,
            shininess: 150
        });
        const nose = new THREE.Mesh(noseGeometry, noseMaterial);
        nose.rotation.x = Math.PI / 2;
        nose.position.z = dims.length * 0.6;
        nose.position.y = dims.height * 0.1;
        group.add(nose);
        
        return group;
    }

    /**
     * Create car body material
     */
    createCarBodyMaterial() {
        return new THREE.MeshPhongMaterial({
            color: 0x0066ff,
            shininess: 100,
            specular: 0x222222
        });
    }

    /**
     * Create wheels
     */
    createWheels() {
        const dims = this.config.physics.dimensions;
        const wheelRadius = 0.3;
        const wheelWidth = 0.2;
        
        const wheelGeometry = new THREE.CylinderGeometry(wheelRadius, wheelRadius, wheelWidth, 16);
        const wheelMaterial = new THREE.MeshPhongMaterial({ 
            color: 0x333333,
            shininess: 50
        });
        
        // Wheel positions
        const wheelPositions = [
            { x: -dims.width/2, z: dims.length/3 },   // Front left
            { x: dims.width/2, z: dims.length/3 },    // Front right
            { x: -dims.width/2, z: -dims.length/3 },  // Rear left
            { x: dims.width/2, z: -dims.length/3 }    // Rear right
        ];
        
        wheelPositions.forEach((pos, index) => {
            const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
            wheel.rotation.z = Math.PI / 2;
            wheel.position.set(pos.x, -dims.height/3, pos.z);
            wheel.castShadow = true;
            
            this.wheels.push(wheel);
            this.mesh.add(wheel);
        });
    }

    /**
     * Create additional car details
     */
    createCarDetails() {
        // Add lights, spoiler, etc.
        this.createLights();
        this.createSpoiler();
    }

    /**
     * Create car lights
     */
    createLights() {
        const dims = this.config.physics.dimensions;
        
        // Headlights
        const headlightGeometry = new THREE.SphereGeometry(0.1, 8, 8);
        const headlightMaterial = new THREE.MeshBasicMaterial({ 
            color: 0xffffff,
            emissive: 0xffffff,
            emissiveIntensity: 0.5
        });
        
        const leftHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
        leftHeadlight.position.set(-dims.width/3, dims.height/4, dims.length/2);
        this.mesh.add(leftHeadlight);
        
        const rightHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
        rightHeadlight.position.set(dims.width/3, dims.height/4, dims.length/2);
        this.mesh.add(rightHeadlight);
        
        // Taillights
        const taillightMaterial = new THREE.MeshBasicMaterial({ 
            color: 0xff0000,
            emissive: 0xff0000,
            emissiveIntensity: 0.3
        });
        
        const leftTaillight = new THREE.Mesh(headlightGeometry, taillightMaterial);
        leftTaillight.position.set(-dims.width/3, dims.height/4, -dims.length/2);
        this.mesh.add(leftTaillight);
        
        const rightTaillight = new THREE.Mesh(headlightGeometry, taillightMaterial);
        rightTaillight.position.set(dims.width/3, dims.height/4, -dims.length/2);
        this.mesh.add(rightTaillight);
    }

    /**
     * Create spoiler
     */
    createSpoiler() {
        const dims = this.config.physics.dimensions;
        
        const spoilerGeometry = new THREE.BoxGeometry(dims.width * 0.8, 0.1, 0.3);
        const spoilerMaterial = new THREE.MeshPhongMaterial({ 
            color: 0x002244,
            shininess: 100
        });
        
        const spoiler = new THREE.Mesh(spoilerGeometry, spoilerMaterial);
        spoiler.position.set(0, dims.height * 0.6, -dims.length * 0.4);
        spoiler.castShadow = true;
        
        this.mesh.add(spoiler);
    }

    /**
     * Create visual effects
     */
    createVisualEffects(scene) {
        if (this.config.feedback.effects.exhaustTrail) {
            this.createExhaustTrail(scene);
        }
        
        if (this.config.feedback.effects.speedLines) {
            this.createSpeedLines(scene);
        }
    }

    /**
     * Create exhaust trail effect
     */
    createExhaustTrail(scene) {
        // Simple particle system for exhaust
        const particleCount = 50;
        const particles = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        
        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        
        const particleMaterial = new THREE.PointsMaterial({
            color: 0x666666,
            size: 0.1,
            transparent: true,
            opacity: 0.6,
            blending: THREE.AdditiveBlending
        });
        
        this.exhaustTrail = new THREE.Points(particles, particleMaterial);
        scene.add(this.exhaustTrail);
    }

    /**
     * Create speed lines effect
     */
    createSpeedLines(scene) {
        // Speed lines for high-speed effect
        const lineCount = 100;
        const lines = new THREE.BufferGeometry();
        const positions = new Float32Array(lineCount * 6); // 2 points per line
        
        lines.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        
        const lineMaterial = new THREE.LineBasicMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.3,
            blending: THREE.AdditiveBlending
        });
        
        this.speedLines = new THREE.LineSegments(lines, lineMaterial);
        this.speedLines.visible = false;
        scene.add(this.speedLines);
    }

    /**
     * Update car
     */
    update(deltaTime, inputState) {
        // Update physics
        this.physics.update(deltaTime, inputState);
        
        // Update state from physics
        this.updateStateFromPhysics();
        
        // Update visual effects
        this.updateVisualEffects(deltaTime);
        
        // Update audio data
        this.updateAudioData();
        
        // Update wheels animation
        this.updateWheels(deltaTime);
    }

    /**
     * Update state from physics
     */
    updateStateFromPhysics() {
        const physicsState = this.physics.getState();
        
        this.state.speed = physicsState.speed;
        this.state.boost = physicsState.boost;
        this.state.isGrounded = physicsState.isGrounded;
        
        // Position and rotation are automatically updated by physics world
        this.state.position.copy(this.mesh.position);
        this.state.rotation.copy(this.mesh.quaternion);
    }

    /**
     * Update visual effects
     */
    updateVisualEffects(deltaTime) {
        // Update speed lines visibility
        if (this.speedLines) {
            const showSpeedLines = this.state.speed > 150; // Show at high speed
            this.speedLines.visible = showSpeedLines;
            
            if (showSpeedLines) {
                this.updateSpeedLinesEffect();
            }
        }
        
        // Update exhaust trail
        if (this.exhaustTrail) {
            this.updateExhaustTrail(deltaTime);
        }
    }

    /**
     * Update speed lines effect
     */
    updateSpeedLinesEffect() {
        // Animate speed lines around the car
        const positions = this.speedLines.geometry.attributes.position.array;
        const carPos = this.mesh.position;
        
        for (let i = 0; i < positions.length; i += 6) {
            // Random position around car
            const angle = Math.random() * Math.PI * 2;
            const radius = 20 + Math.random() * 30;
            const x = carPos.x + Math.cos(angle) * radius;
            const z = carPos.z + Math.sin(angle) * radius;
            const y = carPos.y + (Math.random() - 0.5) * 10;
            
            // Line start
            positions[i] = x;
            positions[i + 1] = y;
            positions[i + 2] = z;
            
            // Line end (slightly offset)
            positions[i + 3] = x + (Math.random() - 0.5) * 2;
            positions[i + 4] = y + (Math.random() - 0.5) * 2;
            positions[i + 5] = z + (Math.random() - 0.5) * 2;
        }
        
        this.speedLines.geometry.attributes.position.needsUpdate = true;
    }

    /**
     * Update exhaust trail
     */
    updateExhaustTrail(deltaTime) {
        // Simple exhaust particle animation
        // In a real implementation, this would be more sophisticated
    }

    /**
     * Update audio data
     */
    updateAudioData() {
        const physicsState = this.physics.getState();
        
        this.audioData = {
            speed: physicsState.speed,
            rpm: physicsState.rpm,
            throttle: physicsState.throttle,
            boost: physicsState.boostActive
        };
    }

    /**
     * Update wheel animations
     */
    updateWheels(deltaTime) {
        const speed = this.state.speed;
        const wheelRotationSpeed = speed * 0.1; // Adjust for visual effect
        
        this.wheels.forEach(wheel => {
            wheel.rotation.x += wheelRotationSpeed * deltaTime;
        });
    }

    /**
     * Get car speed in km/h
     */
    getSpeed() {
        return this.state.speed;
    }

    /**
     * Get boost percentage
     */
    getBoost() {
        return this.state.boost;
    }

    /**
     * Get car position
     */
    getPosition() {
        return this.state.position.clone();
    }

    /**
     * Get audio data for audio system
     */
    getAudioData() {
        return { ...this.audioData };
    }

    /**
     * Set car position and rotation
     */
    setPosition(position, rotation) {
        if (this.physics && this.physics.body) {
            this.physics.body.position.copy(position);
            this.physics.body.quaternion.copy(rotation);
        }
    }

    /**
     * Reset car to specified position
     */
    reset(position, rotation) {
        this.physics.reset(position, rotation);
        this.state.boost = 100;
    }
}
