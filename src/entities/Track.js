import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { TrackGenerator } from '../utils/TrackGenerator.js';

/**
 * Track entity for racing circuit
 * Handles track generation, physics, and visual representation
 */
export class Track {
    constructor(config) {
        this.config = config;
        this.generator = new TrackGenerator(config);
        
        // Track components
        this.mesh = null;
        this.physicsBody = null;
        this.barriers = [];
        this.checkpoints = [];
        
        // Track data
        this.trackData = {
            points: [],
            segments: [],
            spline: null,
            length: 0,
            width: 0
        };
        
        // Start/finish line
        this.startPosition = {
            position: new THREE.Vector3(0, 1, 0),
            rotation: new THREE.Quaternion()
        };
        
        // Materials
        this.materials = {
            track: null,
            barrier: null,
            checkpoint: null
        };
    }

    /**
     * Initialize track
     */
    async init(scene, physicsWorld) {
        console.log('🛣️ Generating track...');
        
        // Generate track data
        this.trackData = await this.generator.generate();
        
        // Create materials
        this.createMaterials();
        
        // Create track mesh
        this.createTrackMesh();
        
        // Create barriers
        this.createBarriers();
        
        // Create checkpoints
        this.createCheckpoints();
        
        // Create physics
        this.createPhysics(physicsWorld);
        
        // Add to scene
        scene.add(this.mesh);
        
        // Set start position
        this.setStartPosition();
        
        console.log('✅ Track generated successfully');
    }

    /**
     * Create track materials
     */
    createMaterials() {
        // Track surface material
        this.materials.track = new THREE.MeshPhongMaterial({
            color: 0x333333,
            shininess: 30,
            specular: 0x111111
        });
        
        // Add track texture if available
        this.addTrackTexture();
        
        // Barrier material
        this.materials.barrier = new THREE.MeshPhongMaterial({
            color: this.config.barriers.effects.color,
            transparent: true,
            opacity: 0.8,
            emissive: this.config.barriers.effects.color,
            emissiveIntensity: 0.2
        });
        
        // Checkpoint material
        this.materials.checkpoint = new THREE.MeshBasicMaterial({
            color: this.config.checkpoints.visual.color,
            transparent: true,
            opacity: 0.6,
            side: THREE.DoubleSide
        });
    }

    /**
     * Add track texture
     */
    addTrackTexture() {
        // Create procedural track texture
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext('2d');
        
        // Draw track surface pattern
        ctx.fillStyle = '#333333';
        ctx.fillRect(0, 0, 512, 512);
        
        // Add lane markings
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
        ctx.setLineDash([20, 10]);
        
        for (let i = 0; i < 512; i += 64) {
            ctx.beginPath();
            ctx.moveTo(i, 0);
            ctx.lineTo(i, 512);
            ctx.stroke();
        }
        
        // Create texture
        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(10, 10);
        
        this.materials.track.map = texture;
    }

    /**
     * Create track mesh
     */
    createTrackMesh() {
        const trackGroup = new THREE.Group();

        if (this.config.type === 'playground') {
            // Create playground mesh
            this.createPlaygroundMesh(trackGroup);
        } else {
            // Create racing track mesh
            const trackGeometry = this.createTrackGeometry();
            const trackMesh = new THREE.Mesh(trackGeometry, this.materials.track);
            trackMesh.receiveShadow = true;
            trackMesh.castShadow = false;

            trackGroup.add(trackMesh);

            // Add boost pads if enabled
            if (this.config.surface.variations.boostPads.enabled) {
                this.createBoostPads(trackGroup);
            }
        }

        this.mesh = trackGroup;
    }

    /**
     * Create playground mesh (large flat surface)
     */
    createPlaygroundMesh(trackGroup) {
        const size = this.config.generation.terrain.size || 500;

        // Create large flat ground plane
        const groundGeometry = new THREE.PlaneGeometry(size, size);
        const groundMesh = new THREE.Mesh(groundGeometry, this.materials.track);
        groundMesh.rotation.x = -Math.PI / 2; // Rotate to be horizontal
        groundMesh.receiveShadow = true;
        groundMesh.castShadow = false;

        trackGroup.add(groundMesh);

        // Add grid if enabled
        if (this.config.environment.grid && this.config.environment.grid.enabled) {
            this.createGridHelper(trackGroup);
        }

        console.log(`🏗️ Created playground mesh: ${size}x${size}m`);
    }

    /**
     * Create grid helper for playground
     */
    createGridHelper(trackGroup) {
        const gridConfig = this.config.environment.grid;
        const size = gridConfig.size || 500;
        const divisions = gridConfig.divisions || 50;
        const color = gridConfig.color || 0x404040;

        const gridHelper = new THREE.GridHelper(size, divisions, color, color);
        gridHelper.material.opacity = gridConfig.opacity || 0.3;
        gridHelper.material.transparent = true;

        trackGroup.add(gridHelper);
    }

    /**
     * Create track geometry
     */
    createTrackGeometry() {
        const points = this.trackData.points;
        const width = this.config.generation.layout.width;
        const segments = points.length;
        
        // Create geometry using track points
        const geometry = new THREE.BufferGeometry();
        const vertices = [];
        const normals = [];
        const uvs = [];
        const indices = [];
        
        // Generate vertices for track surface
        for (let i = 0; i < segments; i++) {
            const point = points[i];
            const nextPoint = points[(i + 1) % segments];
            
            // Calculate track direction
            const direction = new THREE.Vector3()
                .subVectors(nextPoint.position, point.position)
                .normalize();
            
            // Calculate right vector (perpendicular to direction)
            const right = new THREE.Vector3()
                .crossVectors(direction, new THREE.Vector3(0, 1, 0))
                .normalize();
            
            // Create left and right edge points
            const leftPoint = point.position.clone()
                .addScaledVector(right, -width / 2);
            const rightPoint = point.position.clone()
                .addScaledVector(right, width / 2);
            
            // Add vertices
            vertices.push(leftPoint.x, leftPoint.y, leftPoint.z);
            vertices.push(rightPoint.x, rightPoint.y, rightPoint.z);
            
            // Add normals (pointing up)
            normals.push(0, 1, 0);
            normals.push(0, 1, 0);
            
            // Add UVs
            const u = i / segments;
            uvs.push(0, u);
            uvs.push(1, u);
            
            // Add indices for triangles
            if (i < segments - 1) {
                const base = i * 2;
                
                // First triangle
                indices.push(base, base + 1, base + 2);
                // Second triangle
                indices.push(base + 1, base + 3, base + 2);
            }
        }
        
        // Close the loop
        if (this.config.generation.layout.closed) {
            const base = (segments - 1) * 2;
            indices.push(base, base + 1, 0);
            indices.push(base + 1, 1, 0);
        }
        
        geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
        geometry.setAttribute('normal', new THREE.Float32BufferAttribute(normals, 3));
        geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
        geometry.setIndex(indices);
        
        geometry.computeVertexNormals();
        
        return geometry;
    }

    /**
     * Create boost pads
     */
    createBoostPads(trackGroup) {
        const boostConfig = this.config.surface.variations.boostPads;
        const points = this.trackData.points;
        
        for (let i = 0; i < points.length; i += Math.floor(1 / boostConfig.frequency)) {
            if (Math.random() < boostConfig.frequency) {
                const boostPad = this.createBoostPad(points[i]);
                trackGroup.add(boostPad);
            }
        }
    }

    /**
     * Create individual boost pad
     */
    createBoostPad(trackPoint) {
        const geometry = new THREE.PlaneGeometry(
            this.config.generation.layout.width * 0.8,
            this.config.surface.variations.boostPads.length
        );
        
        const material = new THREE.MeshBasicMaterial({
            color: 0x00ffff,
            transparent: true,
            opacity: 0.7,
            emissive: 0x00ffff,
            emissiveIntensity: 0.3
        });
        
        const boostPad = new THREE.Mesh(geometry, material);
        boostPad.rotation.x = -Math.PI / 2;
        boostPad.position.copy(trackPoint.position);
        boostPad.position.y += 0.01; // Slightly above track
        
        return boostPad;
    }

    /**
     * Create track barriers
     */
    createBarriers() {
        if (!this.config.barriers.enabled) return;
        
        const points = this.trackData.points;
        const width = this.config.generation.layout.width;
        const height = this.config.barriers.height;
        
        const barrierGroup = new THREE.Group();
        
        for (let i = 0; i < points.length; i++) {
            const point = points[i];
            const nextPoint = points[(i + 1) % points.length];
            
            // Calculate barrier positions
            const direction = new THREE.Vector3()
                .subVectors(nextPoint.position, point.position)
                .normalize();
            
            const right = new THREE.Vector3()
                .crossVectors(direction, new THREE.Vector3(0, 1, 0))
                .normalize();
            
            // Create left and right barriers
            const leftBarrier = this.createBarrierSegment(point, direction, height);
            leftBarrier.position.addScaledVector(right, -(width / 2 + 1));
            barrierGroup.add(leftBarrier);
            
            const rightBarrier = this.createBarrierSegment(point, direction, height);
            rightBarrier.position.addScaledVector(right, width / 2 + 1);
            barrierGroup.add(rightBarrier);
        }
        
        this.barriers = barrierGroup;
        this.mesh.add(barrierGroup);
    }

    /**
     * Create individual barrier segment
     */
    createBarrierSegment(point, direction, height) {
        const geometry = new THREE.BoxGeometry(
            this.config.barriers.thickness,
            height,
            2 // Segment length
        );
        
        const barrier = new THREE.Mesh(geometry, this.materials.barrier);
        barrier.position.copy(point.position);
        barrier.position.y += height / 2;
        barrier.castShadow = true;
        
        // Orient barrier along track direction
        barrier.lookAt(
            point.position.x + direction.x,
            point.position.y,
            point.position.z + direction.z
        );
        
        return barrier;
    }

    /**
     * Create checkpoints
     */
    createCheckpoints() {
        if (!this.config.checkpoints.enabled) return;
        
        const points = this.trackData.points;
        const checkpointInterval = Math.floor(points.length / this.config.checkpoints.count);
        
        for (let i = 0; i < this.config.checkpoints.count; i++) {
            const pointIndex = i * checkpointInterval;
            const checkpoint = this.createCheckpoint(points[pointIndex], i);
            this.checkpoints.push(checkpoint);
            this.mesh.add(checkpoint);
        }
    }

    /**
     * Create individual checkpoint
     */
    createCheckpoint(trackPoint, index) {
        const geometry = new THREE.PlaneGeometry(
            this.config.checkpoints.width,
            this.config.checkpoints.height
        );
        
        const checkpoint = new THREE.Mesh(geometry, this.materials.checkpoint);
        checkpoint.position.copy(trackPoint.position);
        checkpoint.position.y += this.config.checkpoints.height / 2;
        
        // Add checkpoint data
        checkpoint.userData = {
            index: index,
            isStartFinish: index === 0
        };
        
        return checkpoint;
    }

    /**
     * Create physics bodies
     */
    createPhysics(physicsWorld) {
        // Create track surface physics
        this.createTrackPhysics(physicsWorld);
        
        // Create barrier physics
        if (this.config.barriers.enabled) {
            this.createBarrierPhysics(physicsWorld);
        }
    }

    /**
     * Create track surface physics
     */
    createTrackPhysics(physicsWorld) {
        if (this.config.type === 'playground') {
            // Create a large flat ground plane for playground
            this.createPlaygroundPhysics(physicsWorld);
        } else {
            // Create racing track physics
            this.createRacingTrackPhysics(physicsWorld);
        }
    }

    /**
     * Create playground physics (large flat plane)
     */
    createPlaygroundPhysics(physicsWorld) {
        const size = this.config.generation.terrain.size || 500;

        // Create a large box as ground instead of infinite plane for better collision detection
        const groundBody = physicsWorld.createBox(
            { x: size, y: 1, z: size },
            0,
            physicsWorld.getMaterial('track')
        );

        // Position the ground plane
        groundBody.position.set(0, -0.5, 0);

        // Set collision groups for ground detection
        groundBody.collisionFilterGroup = physicsWorld.getCollisionGroup('TRACK');
        groundBody.collisionFilterMask = physicsWorld.getCollisionGroup('CAR');

        physicsWorld.addBody(groundBody);
        this.physicsBody = groundBody;

        console.log(`🏗️ Created playground physics: ${size}x${size}m`);
    }

    /**
     * Create racing track physics
     */
    createRacingTrackPhysics(physicsWorld) {
        // Create a ground plane for racing tracks
        const groundBody = physicsWorld.createPlane(physicsWorld.getMaterial('track'));
        groundBody.quaternion.setFromAxisAngle(new CANNON.Vec3(1, 0, 0), -Math.PI / 2);

        // Set collision groups for ground detection
        groundBody.collisionFilterGroup = physicsWorld.getCollisionGroup('TRACK');
        groundBody.collisionFilterMask = physicsWorld.getCollisionGroup('CAR');

        physicsWorld.addBody(groundBody);
        this.physicsBody = groundBody;

        console.log('🏗️ Created racing track physics');
    }

    /**
     * Create barrier physics
     */
    createBarrierPhysics(physicsWorld) {
        // Create physics bodies for barriers
        // This would create collision boxes for each barrier segment
    }

    /**
     * Set start position
     */
    setStartPosition() {
        if (this.config.type === 'playground') {
            // For playground, use spawn position from config
            const spawn = this.config.spawn || { position: { x: 0, y: 2, z: 0 }, rotation: { x: 0, y: 0, z: 0 } };
            this.startPosition.position.set(spawn.position.x, spawn.position.y, spawn.position.z);
            this.startPosition.rotation.setFromEuler(new THREE.Euler(spawn.rotation.x, spawn.rotation.y, spawn.rotation.z));
        } else if (this.trackData.points.length > 0) {
            const startPoint = this.trackData.points[0];
            this.startPosition.position.copy(startPoint.position);
            this.startPosition.position.y += 1; // Above track surface

            // Calculate start rotation based on track direction
            if (this.trackData.points.length > 1) {
                const direction = new THREE.Vector3()
                    .subVectors(this.trackData.points[1].position, startPoint.position)
                    .normalize();

                this.startPosition.rotation.setFromUnitVectors(
                    new THREE.Vector3(0, 0, 1),
                    direction
                );
            }
        } else {
            // Fallback to origin
            this.startPosition.position.set(0, 2, 0);
            this.startPosition.rotation.set(0, 0, 0, 1);
        }
    }

    /**
     * Get start position for car placement
     */
    getStartPosition() {
        return {
            position: this.startPosition.position.clone(),
            rotation: this.startPosition.rotation.clone()
        };
    }

    /**
     * Get track length
     */
    getLength() {
        return this.trackData.length;
    }

    /**
     * Get track width
     */
    getWidth() {
        return this.config.generation.layout.width;
    }

    /**
     * Check checkpoint collision
     */
    checkCheckpointCollision(position, tolerance = 5) {
        for (const checkpoint of this.checkpoints) {
            const distance = position.distanceTo(checkpoint.position);
            if (distance < tolerance) {
                return checkpoint.userData;
            }
        }
        return null;
    }
}
