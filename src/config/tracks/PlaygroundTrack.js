/**
 * Playground Track Configuration
 * A simple flat track for testing car movement and controls
 */
export const PlaygroundTrackConfig = {
    // Track metadata
    name: "Playground",
    description: "Open flat area for testing and free driving",
    difficulty: "beginner",
    type: "playground",
    
    // Track generation settings
    generation: {
        // Track layout
        layout: {
            totalLength: 0,       // No predefined track path
            width: 50,            // Large open area
            segments: 0,          // No segments
            closed: false,        // Not a loop
            
            // No elevation changes for flat playground
            elevation: {
                enabled: false,
                maxHeight: 0,
                frequency: 0,
                smoothness: 1.0
            },
            
            // No banking for flat playground
            banking: {
                enabled: false,
                maxAngle: 0,
                cornerMultiplier: 0,
                smoothness: 1.0
            }
        },
        
        // Terrain generation
        terrain: {
            enabled: true,
            type: 'flat',         // Flat terrain type
            size: 500,            // Large playground area (500x500 meters)
            height: 0,            // Completely flat
            roughness: 0          // No roughness
        }
    },
    
    // Track surface properties
    surface: {
        material: 'playground',   // Special playground material
        grip: 1.2,               // Good grip for testing
        friction: 0.9,           // High friction
        
        // No surface variations for clean testing
        variations: {
            enabled: false,
            boostPads: {
                enabled: false,
                frequency: 0,
                length: 0,
                speedMultiplier: 1.0
            },
            
            roughSections: {
                enabled: false,
                frequency: 0,
                length: 0,
                gripReduction: 1.0
            }
        }
    },
    
    // No barriers for open playground
    barriers: {
        enabled: false,
        height: 0,
        thickness: 0,
        material: 'barrier',
        
        visual: {
            enabled: false,
            style: 'none',
            color: 0x666666,
            emissive: 0x000000,
            animation: false
        }
    },
    
    // No checkpoints for playground
    checkpoints: {
        enabled: false,
        count: 0,
        width: 0,
        height: 0,
        
        visual: {
            enabled: false,
            style: 'none',
            color: 0x00ff00,
            animation: false
        },
        
        timing: {
            tolerance: 0,
            required: false
        }
    },
    
    // Playground environment
    environment: {
        // Simple flat terrain
        terrain: {
            enabled: true,
            type: 'minimal',      // Minimal environment
            detail: 'low',        // Low detail for performance
            
            generation: {
                size: 500,        // 500x500 meter area
                height: 0,        // Completely flat
                roughness: 0      // No terrain roughness
            }
        },
        
        // Clear atmosphere for testing
        atmosphere: {
            fog: {
                enabled: false,   // No fog for clear visibility
                density: 0,
                color: 0x87CEEB
            },
            
            lighting: {
                ambient: 0.6,     // Bright ambient lighting
                directional: 1.2, // Strong directional light
                shadows: true     // Enable shadows for depth perception
            },
            
            particles: {
                enabled: false,   // No particles for clean testing
                type: 'none',
                density: 0
            }
        },
        
        // Grid lines for reference (optional)
        grid: {
            enabled: true,        // Show grid for spatial reference
            size: 500,            // Grid covers full area
            divisions: 50,        // 10m grid squares
            color: 0x404040,      // Dark gray grid
            opacity: 0.3          // Semi-transparent
        }
    },
    
    // Spawn settings
    spawn: {
        position: {
            x: 0,
            y: 2,                 // Spawn 2 meters above ground
            z: 0
        },
        rotation: {
            x: 0,
            y: 0,
            z: 0
        }
    }
};
