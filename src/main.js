import { Game } from './core/Game.js';
import { GameConfig } from './config/GameConfig.js';

/**
 * Main entry point for the WebRacer game
 * Initializes the game and handles global setup
 */
class WebRacer {
    constructor() {
        this.game = null;
        this.isInitialized = false;
    }

    /**
     * Initialize the game
     */
    async init() {
        try {
            console.log('🏎️ Initializing WebRacer...');
            
            // Show loading screen
            this.showLoading(true);
            
            // Create game instance
            this.game = new Game(GameConfig);
            
            // Initialize game systems
            await this.game.init();
            
            // Hide loading screen and show game UI
            this.showLoading(false);
            this.showGameUI(true);
            
            // Start the game
            this.game.start();
            
            this.isInitialized = true;
            console.log('🏁 WebRacer initialized successfully!');
            
        } catch (error) {
            console.error('❌ Failed to initialize WebRacer:', error);
            this.showError(error.message);
        }
    }

    /**
     * Show/hide loading screen
     */
    showLoading(show) {
        const loadingElement = document.getElementById('loading');
        if (loadingElement) {
            loadingElement.classList.toggle('hidden', !show);
        }
    }

    /**
     * Show/hide game UI
     */
    showGameUI(show) {
        const elements = ['hud', 'speedometer', 'controls'];
        elements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.classList.toggle('hidden', !show);
            }
        });
    }

    /**
     * Show error message
     */
    showError(message) {
        const loadingElement = document.getElementById('loading');
        if (loadingElement) {
            loadingElement.innerHTML = `
                <div style="color: #ff4444;">
                    <div>❌ Error Loading Game</div>
                    <div style="margin-top: 10px; font-size: 16px;">${message}</div>
                    <div style="margin-top: 10px; font-size: 14px;">Check console for details</div>
                </div>
            `;
        }
    }

    /**
     * Handle window resize
     */
    onWindowResize() {
        if (this.game && this.isInitialized) {
            this.game.onWindowResize();
        }
    }

    /**
     * Handle visibility change (tab focus/blur)
     */
    onVisibilityChange() {
        if (this.game && this.isInitialized) {
            if (document.hidden) {
                this.game.pause();
            } else {
                this.game.resume();
            }
        }
    }

    /**
     * Cleanup on page unload
     */
    cleanup() {
        if (this.game) {
            this.game.destroy();
        }
    }
}

// Initialize the game when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    const webRacer = new WebRacer();

    // Expose for debugging
    window.webRacer = webRacer;

    // Set up event listeners
    window.addEventListener('resize', () => webRacer.onWindowResize());
    document.addEventListener('visibilitychange', () => webRacer.onVisibilityChange());
    window.addEventListener('beforeunload', () => webRacer.cleanup());

    // Start the game
    await webRacer.init();
});

// Global error handling
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
});
