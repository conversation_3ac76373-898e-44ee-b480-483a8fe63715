import * as THREE from 'three';
import { Scene } from './Scene.js';
import { GameLoop } from './GameLoop.js';
import { PhysicsWorld } from '../physics/PhysicsWorld.js';
import { Car } from '../entities/Car.js';
import { Track } from '../entities/Track.js';
import { InputSystem } from '../systems/InputSystem.js';
import { CameraSystem } from '../systems/CameraSystem.js';
import { RenderSystem } from '../systems/RenderSystem.js';
import { AudioSystem } from '../systems/AudioSystem.js';
import { CarConfig } from '../config/CarConfig.js';
import { TrackConfig } from '../config/TrackConfig.js';

/**
 * Main Game class - orchestrates all game systems
 */
export class Game {
    constructor(config) {
        this.config = config;
        this.isInitialized = false;
        this.isPaused = false;
        this.isRunning = false;
        
        // Core systems
        this.scene = null;
        this.gameLoop = null;
        this.physics = null;
        this.input = null;
        this.camera = null;
        this.renderer = null;
        this.audio = null;
        
        // Game entities
        this.car = null;
        this.track = null;
        
        // Game state
        this.gameState = {
            currentLap: 1,
            lapTime: 0,
            bestLapTime: null,
            totalTime: 0,
            checkpoints: [],
            position: 1,
            speed: 0,
            boost: 100
        };
        
        // Performance monitoring
        this.performance = {
            frameCount: 0,
            lastFPSUpdate: 0,
            currentFPS: 0
        };
    }

    /**
     * Initialize all game systems
     */
    async init() {
        try {
            console.log('🎮 Initializing game systems...');
            
            // Initialize core systems
            await this.initSystems();
            
            // Create game entities
            await this.createEntities();
            
            // Set up game loop
            this.setupGameLoop();
            
            // Set up event listeners
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('✅ Game systems initialized');
            
        } catch (error) {
            console.error('❌ Failed to initialize game:', error);
            throw error;
        }
    }

    /**
     * Initialize core systems
     */
    async initSystems() {
        // Initialize Three.js scene
        this.scene = new Scene(this.config);
        await this.scene.init();
        
        // Initialize physics world
        this.physics = new PhysicsWorld(this.config.physics);
        this.physics.init();
        
        // Initialize input system
        this.input = new InputSystem();
        this.input.init();
        
        // Initialize camera system
        this.camera = new CameraSystem(this.config.camera);
        this.camera.init(this.scene.threeScene);
        
        // Initialize render system
        this.renderer = new RenderSystem(this.config.renderer);
        this.renderer.init(this.scene.threeScene, this.camera.camera);
        
        // Initialize audio system
        this.audio = new AudioSystem();
        await this.audio.init();
        
        // Initialize game loop
        this.gameLoop = new GameLoop(this.config.performance.targetFPS);
    }

    /**
     * Create game entities
     */
    async createEntities() {
        // Create track
        this.track = new Track(TrackConfig);
        await this.track.init(this.scene.threeScene, this.physics);

        // Create car
        this.car = new Car(CarConfig);
        await this.car.init(this.scene.threeScene, this.physics);
        
        // Position car at track start
        const startPosition = this.track.getStartPosition();
        this.car.setPosition(startPosition.position, startPosition.rotation);
        
        // Set camera target
        this.camera.setTarget(this.car.mesh);
    }

    /**
     * Set up game loop
     */
    setupGameLoop() {
        this.gameLoop.onUpdate = (deltaTime) => this.update(deltaTime);
        this.gameLoop.onRender = () => this.render();
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Window resize
        window.addEventListener('resize', () => this.onWindowResize());
        
        // Keyboard events for debug
        window.addEventListener('keydown', (event) => {
            switch (event.code) {
                case 'KeyR':
                    this.resetCar();
                    break;
                case 'KeyC':
                    this.camera.switchMode();
                    break;
                case 'KeyP':
                    this.togglePause();
                    break;
            }
        });
    }

    /**
     * Start the game
     */
    start() {
        if (!this.isInitialized) {
            throw new Error('Game not initialized');
        }
        
        console.log('🏁 Starting game...');
        this.isRunning = true;
        this.gameLoop.start();
    }

    /**
     * Update game logic
     */
    update(deltaTime) {
        if (this.isPaused) return;
        
        // Update physics
        this.physics.step(deltaTime);
        
        // Update input
        const inputState = this.input.getState();
        
        // Update car
        this.car.update(deltaTime, inputState);
        
        // Update camera
        this.camera.update(deltaTime);
        
        // Update audio
        this.audio.update(deltaTime, this.car.getAudioData());
        
        // Update game state
        this.updateGameState(deltaTime);
        
        // Update UI
        this.updateUI();
        
        // Performance monitoring
        this.updatePerformance();
    }

    /**
     * Render the game
     */
    render() {
        this.renderer.render();
    }

    /**
     * Update game state
     */
    updateGameState(deltaTime) {
        // Update timers
        this.gameState.lapTime += deltaTime;
        this.gameState.totalTime += deltaTime;
        
        // Update speed
        this.gameState.speed = this.car.getSpeed();
        
        // Update boost
        this.gameState.boost = this.car.getBoost();
        
        // Check for lap completion
        this.checkLapCompletion();
        
        // Check for car reset conditions
        this.checkResetConditions();
    }

    /**
     * Update UI elements
     */
    updateUI() {
        // Update speedometer
        const speedElement = document.getElementById('speed');
        if (speedElement) {
            speedElement.textContent = Math.round(this.gameState.speed);
        }
        
        // Update boost
        const boostElement = document.getElementById('boost');
        if (boostElement) {
            boostElement.textContent = Math.round(this.gameState.boost);
        }
        
        // Update lap counter
        const lapElement = document.getElementById('lapCount');
        if (lapElement) {
            lapElement.textContent = this.gameState.currentLap;
        }
        
        // Update best lap time
        const bestLapElement = document.getElementById('bestLap');
        if (bestLapElement && this.gameState.bestLapTime) {
            bestLapElement.textContent = this.formatTime(this.gameState.bestLapTime);
        }
    }

    /**
     * Check for lap completion
     */
    checkLapCompletion() {
        // This will be implemented when track checkpoint system is ready
        // For now, just a placeholder
    }

    /**
     * Check if car needs to be reset
     */
    checkResetConditions() {
        const carPosition = this.car.getPosition();
        
        // Reset if car falls below reset height
        if (carPosition.y < this.config.gameplay.resetHeight) {
            this.resetCar();
        }
    }

    /**
     * Reset car to track start
     */
    resetCar() {
        const startPosition = this.track.getStartPosition();
        this.car.reset(startPosition.position, startPosition.rotation);
    }

    /**
     * Update performance monitoring
     */
    updatePerformance() {
        this.performance.frameCount++;
        const now = performance.now();
        
        if (now - this.performance.lastFPSUpdate >= 1000) {
            this.performance.currentFPS = this.performance.frameCount;
            this.performance.frameCount = 0;
            this.performance.lastFPSUpdate = now;
            
            if (this.config.debug.logPerformance) {
                console.log(`FPS: ${this.performance.currentFPS}`);
            }
        }
    }

    /**
     * Handle window resize
     */
    onWindowResize() {
        if (this.camera) {
            this.camera.onWindowResize();
        }
        if (this.renderer) {
            this.renderer.onWindowResize();
        }
    }

    /**
     * Pause the game
     */
    pause() {
        this.isPaused = true;
        this.gameLoop.pause();
    }

    /**
     * Resume the game
     */
    resume() {
        this.isPaused = false;
        this.gameLoop.resume();
    }

    /**
     * Toggle pause state
     */
    togglePause() {
        if (this.isPaused) {
            this.resume();
        } else {
            this.pause();
        }
    }

    /**
     * Format time in MM:SS format
     */
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const secs = (seconds % 60).toFixed(2);
        return `${minutes}:${secs.padStart(5, '0')}`;
    }

    /**
     * Cleanup and destroy game
     */
    destroy() {
        if (this.gameLoop) {
            this.gameLoop.stop();
        }
        
        if (this.renderer) {
            this.renderer.destroy();
        }
        
        if (this.audio) {
            this.audio.destroy();
        }
        
        this.isRunning = false;
        this.isInitialized = false;
    }
}
